'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button, Card, Input, Icon, IconButton } from '@/components/ui';
import {
  UserIcons,
  FinanceIcons,
  FeatureIcons,
  RewardIcons,
  ActionIcons
} from '@/config/icons';
import Header from '@/components/layout/Header';
import { useWallet } from '@/hooks/useWallet';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
// import { useHAOX } from '@/hooks/useHAOX'; // 暂时注释，等待重构
import { formatCurrency, formatRelativeTime } from '@/lib/utils';

export default function DashboardPage() {
  const { balance, walletAddress } = useWallet();
  const { user, isAuthenticated } = useTelegramAuth();

  // 从balance对象中提取HAOX余额
  const haoxBalance = balance?.haoxBalance || '0.00';
  const isConnected = isAuthenticated && !!walletAddress;
  
  const [activeTab, setActiveTab] = useState('overview');
  const [isCopied, setIsCopied] = useState(false);

  // Mock user data for display (补充用户信息)
  const userStats = {
    username: user?.username || 'crypto_trader',
    email: '<EMAIL>', // Telegram用户没有email，使用默认值
    joinDate: '2024-01-15',
    isMerchant: false,
    merchantStatus: null,
    totalTrades: 25,
    totalVolume: 125000,
    rewardsEarned: 1250,
    winRate: 73.5,
    rank: 156
  };

  // Mock transaction history
  const transactions = [
    {
      id: '1',
      type: 'buy',
      amount: '1000',
      price: 0.25,
      total: 250,
      status: 'completed',
      date: '2024-01-20T10:30:00Z',
    },
    {
      id: '2',
      type: 'sell',
      amount: '500',
      price: 0.26,
      total: 130,
      status: 'completed',
      date: '2024-01-19T15:45:00Z',
    },
    {
      id: '3',
      type: 'buy',
      amount: '2000',
      price: 0.24,
      total: 480,
      status: 'pending',
      date: '2024-01-18T09:15:00Z',
    },
  ];

  // Mock social tasks
  const socialTasks = [
    {
      id: '1',
      title: '关注官方Twitter',
      platform: 'twitter',
      reward: 50,
      completed: true,
      completedAt: '2024-01-15T12:00:00Z',
    },
    {
      id: '2',
      title: '加入Discord社区',
      platform: 'discord',
      reward: 100,
      completed: true,
      completedAt: '2024-01-16T14:30:00Z',
    },
    {
      id: '3',
      title: '分享推荐链接',
      platform: 'telegram',
      reward: 200,
      completed: false,
      completedAt: null,
    },
  ];

  const copyAddress = async () => {
    if (walletAddress) {
      try {
        await navigator.clipboard.writeText(walletAddress);
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000);
      } catch (error) {
        console.error('Failed to copy address:', error);
      }
    }
  };

  const tabs = [
    { id: 'overview', label: '概览', icon: FinanceIcons.trendUp },
    { id: 'transactions', label: '交易记录', icon: FeatureIcons.history },
    { id: 'tasks', label: '社交任务', icon: RewardIcons.award },
    { id: 'profile', label: '个人资料', icon: UserIcons.user },
    { id: 'settings', label: '设置', icon: FeatureIcons.settings },
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-system-blue/10 rounded-xl flex items-center justify-center">
              <Icon icon={FinanceIcons.wallet} size="lg" color="primary" />
            </div>
            <div>
              <p className="text-caption-1 text-secondary-label">HAOX余额</p>
              <p className="text-title-3 font-sf-pro font-bold text-label">
                {haoxBalance}
              </p>
            </div>
          </div>
        </Card>

        <Card>
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-system-green/10 rounded-xl flex items-center justify-center">
              <Icon icon={FinanceIcons.trendUp} size="lg" color="success" />
            </div>
            <div>
              <p className="text-caption-1 text-secondary-label">总交易量</p>
              <p className="text-title-3 font-sf-pro font-bold text-label">
                {formatCurrency(userStats.totalVolume)}
              </p>
            </div>
          </div>
        </Card>

        <Card>
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-system-orange/10 rounded-xl flex items-center justify-center">
              <Icon icon={RewardIcons.award} size="lg" color="warning" />
            </div>
            <div>
              <p className="text-caption-1 text-secondary-label">获得奖励</p>
              <p className="text-title-3 font-sf-pro font-bold text-label">
                {userStats.rewardsEarned} HAOX
              </p>
            </div>
          </div>
        </Card>

        <Card>
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-system-purple/10 rounded-xl flex items-center justify-center">
              <Icon icon={FeatureIcons.history} size="lg" color="secondary" />
            </div>
            <div>
              <p className="text-caption-1 text-secondary-label">交易次数</p>
              <p className="text-title-3 font-sf-pro font-bold text-label">
                {userStats.totalTrades}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Wallet Info */}
      {isConnected && (
        <Card title="钱包信息">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-body text-secondary-label">钱包地址</span>
              <div className="flex items-center space-x-2">
                <span className="text-body font-mono font-medium text-label">
                  {walletAddress?.slice(0, 6)}...{walletAddress?.slice(-4)}
                </span>
                <IconButton
                  icon={isCopied ? ActionIcons.check : ActionIcons.copy}
                  onClick={copyAddress}
                  size="sm"
                  variant="ghost"
                  color={isCopied ? 'success' : 'muted'}
                  aria-label={isCopied ? '已复制' : '复制地址'}
                />
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-body text-secondary-label">BNB余额</span>
              <span className="text-body font-sf-pro font-medium text-label">
                {balance?.bnbBalance || '0.00'} BNB
              </span>
            </div>
          </div>
        </Card>
      )}

      {/* Recent Transactions */}
      <Card title="最近交易">
        <div className="space-y-3">
          {transactions.slice(0, 3).map((tx) => (
            <div key={tx.id} className="flex items-center justify-between p-3 bg-system-gray-6 rounded-xl">
              <div className="flex items-center space-x-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  tx.type === 'buy' ? 'bg-system-green/10' : 'bg-system-red/10'
                }`}>
                  <Icon
                    icon={tx.type === 'buy' ? FinanceIcons.trendUp : FinanceIcons.trendDown}
                    size="sm"
                    className={tx.type === 'buy' ? 'text-system-green' : 'text-system-red'}
                  />
                </div>
                <div>
                  <p className="text-body font-sf-pro font-medium text-label">
                    {tx.type === 'buy' ? '买入' : '卖出'} {tx.amount} HAOX
                  </p>
                  <p className="text-caption-1 text-secondary-label">
                    {formatRelativeTime(tx.date)}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-body font-sf-pro font-medium text-label">
                  {formatCurrency(tx.total)}
                </p>
                <p className={`text-caption-1 ${
                  tx.status === 'completed' ? 'text-system-green' : 'text-system-orange'
                }`}>
                  {tx.status === 'completed' ? '已完成' : '处理中'}
                </p>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );

  const renderTransactions = () => (
    <Card title="交易记录">
      <div className="space-y-4">
        {transactions.map((tx) => (
          <div key={tx.id} className="flex items-center justify-between p-4 border border-system-gray-4 rounded-xl">
            <div className="flex items-center space-x-4">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                tx.type === 'buy' ? 'bg-system-green/10' : 'bg-system-red/10'
              }`}>
                <Icon
                  icon={tx.type === 'buy' ? FinanceIcons.trendUp : FinanceIcons.trendDown}
                  size="md"
                  className={tx.type === 'buy' ? 'text-system-green' : 'text-system-red'}
                />
              </div>
              <div>
                <p className="text-body font-sf-pro font-medium text-label">
                  {tx.type === 'buy' ? '买入' : '卖出'} {tx.amount} HAOX
                </p>
                <p className="text-caption-1 text-secondary-label">
                  单价: ¥{tx.price.toFixed(4)} • {formatRelativeTime(tx.date)}
                </p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-body font-sf-pro font-medium text-label">
                {formatCurrency(tx.total)}
              </p>
              <p className={`text-caption-1 ${
                tx.status === 'completed' ? 'text-system-green' : 'text-system-orange'
              }`}>
                {tx.status === 'completed' ? '已完成' : '处理中'}
              </p>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );

  const renderTasks = () => (
    <Card title="社交任务">
      <div className="space-y-4">
        {socialTasks.map((task) => (
          <div key={task.id} className="flex items-center justify-between p-4 border border-system-gray-4 rounded-xl">
            <div className="flex items-center space-x-4">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                task.completed ? 'bg-system-green/10' : 'bg-system-gray-5'
              }`}>
                <Icon
                  icon={RewardIcons.award}
                  size="md"
                  className={task.completed ? 'text-system-green' : 'text-secondary-label'}
                />
              </div>
              <div>
                <p className="text-body font-sf-pro font-medium text-label">
                  {task.title}
                </p>
                <p className="text-caption-1 text-secondary-label">
                  奖励: {task.reward} HAOX
                  {task.completed && task.completedAt && (
                    <span> • 完成于 {formatRelativeTime(task.completedAt)}</span>
                  )}
                </p>
              </div>
            </div>
            <div>
              {task.completed ? (
                <span className="text-system-green text-sm font-sf-pro font-medium">
                  已完成
                </span>
              ) : (
                <Button size="sm">
                  完成任务
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>
    </Card>
  );

  const renderProfile = () => (
    <div className="space-y-6">
      <Card title="个人信息">
        <div className="space-y-4">
          <Input
            label="用户名"
            value={userStats.username}
            onChange={() => {}}
            disabled
          />
          <Input
            label="邮箱"
            value={userStats.email}
            onChange={() => {}}
            disabled
          />
          <div>
            <label className="block text-sm font-medium font-sf-pro mb-2 text-label">
              注册时间
            </label>
            <p className="text-body text-secondary-label">
              {new Date(userStats.joinDate).toLocaleDateString('zh-CN')}
            </p>
          </div>
        </div>
      </Card>

      <Card title="商家认证">
        <div className="text-center py-6">
          <Icon icon={UserIcons.shield} size="2xl" color="muted" className="mx-auto mb-4" />
          <h3 className="text-headline font-sf-pro font-semibold text-label mb-2">
            申请成为认证商家
          </h3>
          <p className="text-body text-secondary-label mb-4">
            成为认证商家，享受更多特权和更低的交易费率
          </p>
          <Button>
            申请认证
          </Button>
        </div>
      </Card>
    </div>
  );

  return (
    <div className="min-h-screen bg-system-background">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className="lg:w-64">
            <Card>
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-system-blue rounded-full flex items-center justify-center mx-auto mb-3">
                  <Icon icon={UserIcons.user} size="xl" className="text-white" />
                </div>
                <h2 className="text-title-3 font-sf-pro font-bold text-label">
                  {userStats.username}
                </h2>
                <p className="text-body text-secondary-label">
                  {userStats.email}
                </p>
              </div>

              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-xl transition-colors ${
                      activeTab === tab.id
                        ? 'bg-system-blue text-white'
                        : 'text-secondary-label hover:text-label hover:bg-system-gray-6'
                    }`}
                  >
                    <tab.icon className="w-5 h-5" />
                    <span className="text-body font-sf-pro font-medium">
                      {tab.label}
                    </span>
                  </button>
                ))}
              </nav>
            </Card>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {activeTab === 'overview' && renderOverview()}
              {activeTab === 'transactions' && renderTransactions()}
              {activeTab === 'tasks' && renderTasks()}
              {activeTab === 'profile' && renderProfile()}
              {activeTab === 'settings' && (
                <Card title="设置">
                  <p className="text-body text-secondary-label">
                    设置功能开发中...
                  </p>
                </Card>
              )}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
